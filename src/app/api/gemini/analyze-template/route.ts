import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { readFile } from 'fs/promises';
import { join } from 'path';

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);

export async function POST(request: NextRequest) {
  let markdown = '';

  try {
    console.log('🔍 GEMINI TEMPLATE ANALYSIS: Starting intelligent template scan...');

    const requestData = await request.json();
    const { templateId } = requestData;
    markdown = requestData.markdown;

    if (!templateId || !markdown) {
      return NextResponse.json({ error: 'Missing template ID or markdown content' }, { status: 400 });
    }

    console.log('Template ID:', templateId);
    console.log('Markdown length:', markdown.length);

    // Load company settings for context
    const settings = await loadCompanySettings();

    // Analyze template with Gemini AI
    const analysis = await analyzeTemplateWithGemini(markdown, settings);

    console.log('✅ GEMINI ANALYSIS: Template analysis complete');
    console.log('Required fields detected:', analysis.requiredFields.length);
    console.log('Optional fields detected:', analysis.optionalFields.length);

    return NextResponse.json({
      templateId,
      analysis: {
        requiredFields: analysis.requiredFields,
        optionalFields: analysis.optionalFields,
        detectedPlaceholders: analysis.detectedPlaceholders,
        templateType: analysis.templateType,
        complexity: analysis.complexity,
        recommendations: analysis.recommendations,
        missingInformation: analysis.missingInformation,
        autoFillableFields: analysis.autoFillableFields
      },
      success: true
    });

  } catch (error) {
    console.error('❌ GEMINI ANALYSIS: Template analysis failed:', error);
    return NextResponse.json(
      { 
        error: 'Template analysis failed: ' + (error instanceof Error ? error.message : 'Unknown error'),
        fallbackAnalysis: createFallbackAnalysis(markdown)
      },
      { status: 500 }
    );
  }
}

// Load company settings for context
async function loadCompanySettings() {
  try {
    const uploadsDir = join(process.cwd(), 'uploads');
    const settingsPath = join(uploadsDir, 'settings.json');
    const settingsData = await readFile(settingsPath, 'utf-8');
    return JSON.parse(settingsData);
  } catch (error) {
    return getDefaultSettings();
  }
}

function getDefaultSettings() {
  return {
    companyInfo: {
      companyName: 'QuantumRhino',
      contactName: 'Chase Vazquez',
      email: '<EMAIL>',
      phone: '+****************',
      address: '123 Innovation Drive, Tech City, TC 12345',
      website: 'https://quantumrhino.com'
    },
    projectDefaults: {
      defaultProjectType: 'Web Application Development',
      defaultHourlyRate: '150',
      defaultTimeline: '8-12 weeks',
      defaultPaymentTerms: '50% upfront, 50% on completion'
    }
  };
}

// Analyze template with Gemini AI
async function analyzeTemplateWithGemini(markdown: string, settings: any) {
  const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

  const analysisPrompt = `
🔍 **INTELLIGENT TEMPLATE STRUCTURE ANALYSIS**

You are an expert document analyst. Analyze this SOW (Statement of Work) template to understand its STRUCTURE and FIELD REQUIREMENTS only.

**CRITICAL INSTRUCTIONS:**
- IGNORE ALL EXISTING CONTENT/TEXT in the template - treat it as placeholder content that will be replaced
- IGNORE SIGNATURE SECTIONS - do not analyze signature lines, signature blocks, or signature-related content
- Focus ONLY on identifying field structures, placeholders, and document organization
- Extract field requirements based on placeholder patterns and document sections, NOT the actual content
- Treat ALL existing text as sample/placeholder content that will be completely replaced
- Generate field requirements based on what each placeholder/section is MEANT TO REPRESENT, not what it currently contains
- Skip any signature areas, signature lines, or signature-related fields in your analysis

**TEMPLATE STRUCTURE TO ANALYZE:**
${markdown}

**COMPANY CONTEXT (for auto-fillable field identification):**
- Company: ${settings.companyInfo?.companyName || 'QuantumRhino'}
- Contact: ${settings.companyInfo?.contactName || 'Chase Vazquez'}
- Default Rate: $${settings.projectDefaults?.defaultHourlyRate || '150'}/hour
- Default Timeline: ${settings.projectDefaults?.defaultTimeline || '8-12 weeks'}

**ANALYSIS REQUIREMENTS:**

1. **STRUCTURAL FIELD ANALYSIS** - Identify what information slots exist:
   - Find placeholders like [CLIENT_NAME], {PROJECT_NAME}, [CLIENT_COMPANY], etc.
   - Identify document sections that require specific types of information
   - Determine field types based on placeholder names and context (dates, names, descriptions, etc.)
   - Map document structure to required information categories

2. **REQUIRED FIELDS** - Information that MUST be provided by the user:
   - Client-specific information that cannot be auto-filled
   - Project-specific details unique to each engagement
   - Custom content that varies per project
   - Legal or contractual fields requiring client input

3. **OPTIONAL FIELDS** - Information that would enhance the SOW:
   - Additional project details for better customization
   - Enhanced descriptions or specifications
   - Extra terms or conditions specific to the project

4. **AUTO-FILLABLE FIELDS** - Information available from company settings:
   - Vendor/company information (name, contact, address, etc.)
   - Standard rates, terms, and business information
   - Current dates, standard clauses, default values

5. **TEMPLATE CLASSIFICATION**:
   - Template type based on structure (consulting, development, design, etc.)
   - Complexity level based on number and types of fields
   - Document sections and organization pattern

**RESPONSE FORMAT (JSON):**
{
  "requiredFields": [
    {
      "id": "client_name",
      "title": "Client Contact Name",
      "description": "Primary contact person at the client company - required to replace [CLIENT_NAME] placeholder",
      "placeholder": "[CLIENT_NAME] or {CLIENT_NAME}",
      "category": "Client Information",
      "priority": "high",
      "examples": ["John Smith", "Sarah Johnson", "Michael Chen"],
      "inputType": "text",
      "validation": "required",
      "structuralContext": "Found in header/signature sections, referenced throughout document"
    }
  ],
  "optionalFields": [
    {
      "id": "project_timeline_details",
      "title": "Detailed Project Timeline",
      "description": "Specific milestones and deadlines to enhance basic timeline structure",
      "category": "Project Details",
      "priority": "medium",
      "examples": ["Phase 1: Discovery (2 weeks)", "Phase 2: Development (6 weeks)"],
      "structuralContext": "Timeline section allows for detailed breakdown"
    }
  ],
  "autoFillableFields": [
    {
      "id": "company_name",
      "title": "Service Provider Company Name",
      "value": "${settings.companyInfo?.companyName}",
      "source": "company_settings",
      "placeholder": "[VENDOR_NAME] or {COMPANY_NAME}",
      "structuralContext": "Vendor information section"
    }
  ],
  "detectedPlaceholders": ["CLIENT_NAME", "PROJECT_NAME", "START_DATE"],
  "templateType": "software_development_sow",
  "complexity": "moderate",
  "structuralSections": [
    "Header/Title Section",
    "Parties Information",
    "Project Scope",
    "Timeline & Milestones",
    "Payment Terms",
    "Legal Clauses"
  ],
  "recommendations": [
    "Focus on client-specific information first - this populates most placeholders",
    "Define project scope clearly as it drives timeline and budget calculations",
    "Gather payment preferences early as they affect contract terms"
  ],
  "fieldMapping": {
    "CLIENT_NAME": "client_name",
    "PROJECT_NAME": "project_name",
    "START_DATE": "start_date"
  }
}

**CRITICAL ANALYSIS PRINCIPLES:**
- IGNORE all existing content - analyze only the structural requirements
- IGNORE signature sections, signature lines, and signature-related content completely
- Focus on placeholder patterns and document sections, not current text
- Identify what information TYPES are needed, not what content currently exists
- Map placeholders to user input requirements
- Distinguish between user-provided data and auto-fillable company data
- Provide structural context for each field requirement
- Skip signature areas in your field analysis
`;

  try {
    const result = await model.generateContent(analysisPrompt);
    const response = await result.response;
    const text = response.text();

    console.log('🤖 GEMINI ANALYSIS: Raw response length:', text.length);

    // Extract JSON from response
    const jsonMatch = text.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No valid JSON found in Gemini response');
    }

    const analysisData = JSON.parse(jsonMatch[0]);
    
    // Validate and enhance the analysis
    return enhanceAnalysis(analysisData, markdown);

  } catch (error) {
    console.error('❌ GEMINI ANALYSIS: Failed to analyze template:', error);
    throw error;
  }
}

// Enhance analysis with additional processing
function enhanceAnalysis(analysis: any, markdown: string) {
  // Add detected placeholders from markdown if not provided
  if (!analysis.detectedPlaceholders || analysis.detectedPlaceholders.length === 0) {
    analysis.detectedPlaceholders = extractPlaceholders(markdown);
  }

  // Ensure required fields have proper structure
  analysis.requiredFields = analysis.requiredFields?.map((field: any) => ({
    id: field.id || field.title?.toLowerCase().replace(/\s+/g, '_'),
    title: field.title,
    description: field.description,
    placeholder: field.placeholder,
    category: field.category || 'General',
    priority: field.priority || 'medium',
    examples: field.examples || [],
    inputType: field.inputType || 'text',
    validation: field.validation || 'optional'
  })) || [];

  // Add completion estimate
  analysis.estimatedCompletionTime = estimateCompletionTime(analysis);

  return analysis;
}

// Extract placeholders from markdown
function extractPlaceholders(markdown: string): string[] {
  const placeholders = new Set<string>();
  const patterns = [
    /\{([A-Z_]+)\}/g,
    /\[([A-Z_]+)\]/g,
    /\$\{([A-Z_]+)\}/g,
    /\{\{([A-Z_]+)\}\}/g,
  ];

  patterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(markdown)) !== null) {
      placeholders.add(match[1]);
    }
  });

  return Array.from(placeholders);
}

// Estimate completion time based on analysis
function estimateCompletionTime(analysis: any): string {
  const requiredCount = analysis.requiredFields?.length || 0;
  const optionalCount = analysis.optionalFields?.length || 0;
  const totalFields = requiredCount + optionalCount;

  if (totalFields <= 5) return '2-3 minutes';
  if (totalFields <= 10) return '5-7 minutes';
  if (totalFields <= 15) return '8-12 minutes';
  return '15+ minutes';
}

// Create fallback analysis if Gemini fails
function createFallbackAnalysis(markdown: string) {
  const placeholders = extractPlaceholders(markdown);
  
  return {
    requiredFields: [
      {
        id: 'client_name',
        title: 'Client Name',
        description: 'Primary contact person at the client company',
        category: 'Client Information',
        priority: 'high',
        examples: ['John Smith', 'Sarah Johnson'],
        inputType: 'text',
        validation: 'required'
      },
      {
        id: 'project_name',
        title: 'Project Name',
        description: 'Name or title of the project',
        category: 'Project Information',
        priority: 'high',
        examples: ['Website Redesign', 'Mobile App Development'],
        inputType: 'text',
        validation: 'required'
      }
    ],
    optionalFields: [],
    autoFillableFields: [],
    detectedPlaceholders: placeholders,
    templateType: 'general_sow',
    complexity: 'moderate',
    recommendations: ['Fill out client information first', 'Define project scope clearly'],
    missingInformation: ['Client details', 'Project requirements'],
    estimatedCompletionTime: '5-7 minutes'
  };
}
